/* 量子共鸣者 - 主样式文件 */

/* CSS变量定义 - 量子主题色彩 */
:root {
    /* 主色调 - 深空蓝紫色系 */
    --primary-color: #6c5ce7;
    --primary-light: #a29bfe;
    --primary-dark: #5f3dc4;
    
    /* 次要色调 - 量子青色 */
    --secondary-color: #00cec9;
    --secondary-light: #55efc4;
    --secondary-dark: #00b894;
    
    /* 背景色 - 深空渐变 */
    --bg-primary: #0d1421;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-gradient: linear-gradient(135deg, #0d1421 0%, #1a1a2e 50%, #16213e 100%);
    
    /* 文本色 */
    --text-primary: #ffffff;
    --text-secondary: #b2bec3;
    --text-muted: #636e72;
    
    /* 强调色 - 量子光效 */
    --accent-color: #fd79a8;
    --accent-light: #fdcb6e;
    --accent-dark: #e84393;
    
    /* 状态色 */
    --success-color: #00b894;
    --warning-color: #fdcb6e;
    --error-color: #e17055;
    --info-color: #74b9ff;
    
    /* 阴影和光效 */
    --shadow-light: 0 2px 10px rgba(108, 92, 231, 0.1);
    --shadow-medium: 0 4px 20px rgba(108, 92, 231, 0.2);
    --shadow-heavy: 0 8px 40px rgba(108, 92, 231, 0.3);
    --glow-primary: 0 0 20px rgba(108, 92, 231, 0.5);
    --glow-secondary: 0 0 20px rgba(0, 206, 201, 0.5);
    
    /* 尺寸变量 */
    --border-radius: 12px;
    --border-radius-small: 6px;
    --border-radius-large: 20px;
    
    /* 动画变量 */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* 字体 */
    --font-primary: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-gradient);
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 屏幕管理系统 */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
    z-index: 1;
}

.screen.active {
    opacity: 1;
    visibility: visible;
}

.screen.overlay {
    background: rgba(13, 20, 33, 0.9);
    backdrop-filter: blur(10px);
    z-index: 100;
}

/* 背景画布 */
.background-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

/* 加载屏幕样式 */
#loading-screen {
    background: var(--bg-gradient);
    z-index: 1000;
}

.loading-container {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
}

.quantum-logo {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
}

.particle-orbit {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: rotate 3s linear infinite;
}

.particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--secondary-color);
    border-radius: 50%;
    box-shadow: var(--glow-secondary);
}

.particle:nth-child(1) {
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    animation: pulse 1s ease-in-out infinite;
}

.particle:nth-child(2) {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    animation: pulse 1s ease-in-out infinite 0.33s;
}

.particle:nth-child(3) {
    top: 50%;
    right: -4px;
    transform: translateY(-50%);
    animation: pulse 1s ease-in-out infinite 0.66s;
}

.resonance-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    border-radius: 50%;
    animation: resonance 2s ease-in-out infinite;
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.game-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    letter-spacing: 0.1em;
}

.loading-progress {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width var(--transition-medium);
    box-shadow: var(--glow-primary);
}

.loading-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 主菜单样式 */
.menu-container {
    text-align: center;
    max-width: 500px;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.game-header {
    margin-bottom: 3rem;
}

.main-title {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: var(--glow-primary);
}

.main-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    letter-spacing: 0.05em;
}

.main-menu {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
}

.menu-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.menu-btn.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border-color: var(--primary-color);
    box-shadow: var(--glow-primary);
}

.menu-btn.primary:hover {
    background: linear-gradient(45deg, var(--primary-light), var(--secondary-color));
    box-shadow: var(--glow-primary), var(--shadow-heavy);
}

.btn-icon {
    font-size: 1.3rem;
    min-width: 1.5rem;
}

.btn-text {
    flex: 1;
    text-align: left;
}

.menu-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.player-name {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.switch-player-btn {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius-small);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.switch-player-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.language-selector {
    display: flex;
    align-items: center;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius-small);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.lang-btn:hover {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* 动画定义 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.6; 
        transform: scale(1.2);
    }
}

@keyframes resonance {
    0%, 100% { 
        opacity: 0.8; 
        transform: translate(-50%, -50%) scale(1);
    }
    50% { 
        opacity: 0.4; 
        transform: translate(-50%, -50%) scale(1.3);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: var(--glow-primary); }
    50% { box-shadow: var(--glow-primary), var(--glow-secondary); }
}

/* 工具类 */
.hidden {
    display: none !important;
}

.invisible {
    opacity: 0 !important;
    visibility: hidden !important;
}

.no-pointer {
    pointer-events: none !important;
}

.text-center {
    text-align: center !important;
}

.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-light);
}
