{"name": "量子共鸣者", "short_name": "QuantumResonance", "description": "捕捉决定性瞬间，引燃无限可能。通过操控量子粒子的共鸣频率，在多维空间中创造连锁反应。", "version": "1.0.0", "manifest_version": 3, "start_url": "/", "display": "standalone", "orientation": "landscape-primary", "theme_color": "#1a1a2e", "background_color": "#0f0f1e", "scope": "/", "lang": "zh-CN", "dir": "ltr", "categories": ["games", "entertainment"], "screenshots": [{"src": "assets/images/screenshot-1.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "游戏主界面"}, {"src": "assets/images/screenshot-2.png", "sizes": "720x1280", "type": "image/png", "platform": "narrow", "label": "移动端游戏界面"}], "icons": [{"src": "assets/images/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "开始游戏", "short_name": "开始", "description": "直接开始游戏", "url": "/?action=start", "icons": [{"src": "assets/images/shortcut-start.png", "sizes": "96x96"}]}, {"name": "关卡编辑器", "short_name": "编辑器", "description": "打开关卡编辑器", "url": "/?action=editor", "icons": [{"src": "assets/images/shortcut-editor.png", "sizes": "96x96"}]}, {"name": "成就系统", "short_name": "成就", "description": "查看游戏成就", "url": "/?action=achievements", "icons": [{"src": "assets/images/shortcut-achievements.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate", "protocol_handlers": [{"protocol": "web+quantumresonance", "url": "/?protocol=%s"}], "file_handlers": [{"action": "/?file_handler", "accept": {"application/json": [".q<PERSON><PERSON>", ".qrconfig"]}, "icons": [{"src": "assets/images/file-icon.png", "sizes": "256x256", "type": "image/png"}], "launch_type": "single-client"}]}